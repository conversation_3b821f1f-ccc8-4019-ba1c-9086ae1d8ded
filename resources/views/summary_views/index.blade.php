<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Summary Views') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h1 class="text-2xl font-bold mb-4">Summary Views</h1>

                    <a href="{{ route('summary_views.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mb-4">Create New View</a>

                    <table class="w-full">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 bg-gray-100">Name</th>
                                <th class="py-2 px-4 bg-gray-100">Created At</th>
                                <th class="py-2 px-4 bg-gray-100">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($summaryViews as $view)
                            <tr>
                                <td class="border border-slate-300 p-2">{{ $view->name }}</td>
                                <td class="border border-slate-300 p-2">{{ $view->project->name }}</td>
                                <td class="border border-slate-300 p-2">{{ $view->created_at }}</td>
                                <td class="border border-slate-300 p-2">
                                    <a href="{{ route('summary_views.show', $view->id) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">View</a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>