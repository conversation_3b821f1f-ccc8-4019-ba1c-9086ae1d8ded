<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Project Settings') }}
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form method="POST" action="{{ route('projects.update', $project) }}">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <x-input-label for="name" :value="__('Project Name')" />
                            <x-text-input id="name" class="block mt-1 w-full" type="text" name="name" :value="old('name', $project->name)" required autofocus />
                            <x-input-error :messages="$errors->get('name')" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <x-input-label for="api_key" :value="__('API Key')" />
                            <div class="relative">
                                <x-text-input id="api_key" class="block mt-1 w-full pr-10" type="text" name="api_key" :value="old('api_key', $project->api_key)" required />
                            </div>
                            <x-input-error :messages="$errors->get('api_key')" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <x-input-label for="timezone" :value="__('Timezone')" />
                            <select id="timezone" name="timezone" class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                                @foreach (timezone_identifiers_list() as $timezone)
                                    <option value="{{ $timezone }}" {{ old('timezone', $project->timezone ?? 'UTC') == $timezone ? 'selected' : '' }}>
                                        {{ $timezone }}
                                    </option>
                                @endforeach
                            </select>
                            <x-input-error :messages="$errors->get('timezone')" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <x-input-label for="per_page" :value="__('Results per page')" />
                            <div class="relative">
                                <x-text-input id="per_page" class="block mt-1 w-full pr-10" type="number" name="per_page" :value="old('per_page', $project->per_page ?? 50)" required />
                            </div>
                            <x-input-error :messages="$errors->get('per_page')" class="mt-2" />
                        </div>


                        <div class="mb-4">
                            <x-input-label for="ref1" :value="__('REF1')" />
                            <div class="relative">
                                <x-text-input id="ref1" class="block mt-1 w-full pr-10" type="text" name="ref1" :value="old('ref1', @$project->ref_names['ref1'])" />
                            </div>
                            <x-input-error :messages="$errors->get('ref1')" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <x-input-label for="ref2" :value="__('REF2')" />
                            <div class="relative">
                                <x-text-input id="ref2" class="block mt-1 w-full pr-10" type="text" name="ref2" :value="old('ref2', @$project->ref_names['ref2'])" />
                            </div>
                            <x-input-error :messages="$errors->get('ref1')" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <x-input-label for="ref3" :value="__('REF3')" />
                            <div class="relative">
                                <x-text-input id="ref3" class="block mt-1 w-full pr-10" type="text" name="ref3" :value="old('ref3', @$project->ref_names['ref3'])" />
                            </div>
                            <x-input-error :messages="$errors->get('ref1')" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <x-input-label for="ref4" :value="__('REF4')" />
                            <div class="relative">
                                <x-text-input id="ref4" class="block mt-1 w-full pr-10" type="text" name="ref4" :value="old('ref4', @$project->ref_names['ref4'])" />
                            </div>
                            <x-input-error :messages="$errors->get('ref1')" class="mt-2" />
                        </div>

                        <div class="mb-4">
                            <x-input-label for="ref5" :value="__('REF5')" />
                            <div class="relative">
                                <x-text-input id="ref5" class="block mt-1 w-full pr-10" type="text" name="ref5" :value="old('ref5', @$project->ref_names['ref5'])" />
                            </div>
                            <x-input-error :messages="$errors->get('ref1')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-end mt-4">
                            <x-primary-button class="ml-4">
                                {{ __('Update') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
