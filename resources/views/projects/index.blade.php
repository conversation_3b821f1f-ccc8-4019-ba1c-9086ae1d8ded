<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Projects') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">

                <table class="table-auto w-full border-collapse border border-slate-400">
                  <thead>
                    <tr>
                      <th class="bg-slate-200 border border-slate-300 p-4">Name</th>
                      <th class="bg-slate-200 border border-slate-300 p-4">Key</th>
                      <th class="bg-slate-200 border border-slate-300 p-4">Timezone</th>
                      <th class="bg-slate-200 border border-slate-300 p-4"></th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach ($projects as $project)
                    <tr>
                      <td class="border border-slate-300 p-4">{{ $project->name }}</td>
                      <td class="border border-slate-300 p-4">{{ $project->api_key }}</td>
                      <td class="border border-slate-300 p-4">{{ $project->timezone ?? env('APP_TIMEZONE', 'UTC') }}</td>
                      <td class="border border-slate-300 p-4">
                        <a href="{{ route('projects.show', $project->id) }}" class="mr-2 font-medium text-blue-600 dark:text-blue-500 hover:underline">Details</a>
                        <a href="{{ route('projects.edit', $project->id) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Settings</a>
                    </td>
                    </tr>
                    @endforeach
                  </tbody>
                </table>

            </div>
        </div>
    </div>
</x-app-layout>
