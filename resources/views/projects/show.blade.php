<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Project Details') }}
        </h2>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <p class="text-2xl p-2"><strong>Name:</strong> {{ $project->name }}</p>
                <p class="text-2xl p-2"><strong>Key:</strong> {{ $project->api_key }}</p>
                <p class="text-2xl p-2"><strong>Timezone:</strong> {{ $project->timezone }}</p>
            </div>
        </div>
    </div>
    <div class="py-8">
        <div class="max-w-full mx-auto sm:px-6 lg:px-8">
            <div class="p-2 bg-white overflow-hidden shadow-sm sm:rounded-lg">

                <h2 class="text-2xl">Records</h2>

                <form class="bg-white shadow-md rounded my-4" method="post">
                    @csrf
                    <div class="mb-4 flex items-start">
                        <div class="inline-block relative w-full px-2">
                            <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="search-text">
                                Search text
                              </label>
                            <input name="search" id="search-text" value="{{ @$data['search'] }}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="username" type="text" placeholder="search...">
                        </div>
                        <div class="inline-block relative w-48 px-2">
                          <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="search-fields">
                            Search in
                          </label>
                          <select name="in[]" id="search-fields" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" multiple>
                            <option value="" {{ ( !isset($data['in']) || $data['in'][0] == null ) ? 'selected' : '' }}>All fields</option>
                            <option value="id" {{ ( isset($data['in']) && in_array('id', $data['in']) ) ? 'selected' : '' }}>ID</option>
                            <option value="account" {{ ( isset($data['in']) && in_array('account', $data['in']) ) ? 'selected' : '' }}>Account</option>
                            <option value="event" {{ ( isset($data['in']) && in_array('event', $data['in']) ) ? 'selected' : '' }}>Event</option>
                            <option value="status" {{ ( isset($data['in']) && in_array('status', $data['in']) ) ? 'selected' : '' }}>Status</option>
                            <option value="description" {{ ( isset($data['in']) && in_array('description', $data['in']) ) ? 'selected' : '' }}>Description</option>
                            <option value="value" {{ ( isset($data['in']) && in_array('value', $data['in']) ) ? 'selected' : '' }}>Value</option>
                            <option value="custom_content" {{ ( isset($data['in']) && in_array('custom_content', $data['in']) ) ? 'selected' : '' }}>Custom Content</option>
                            <option value="ref1" {{ ( isset($data['in']) && in_array('ref1', $data['in']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref1'])) && !empty($project->ref_names['ref1']) ? $project->ref_names['ref1'] : 'REF1' }}</option>
                            <option value="ref2" {{ ( isset($data['in']) && in_array('ref2', $data['in']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref2'])) && !empty($project->ref_names['ref2']) ? $project->ref_names['ref2'] : 'REF2' }}</option>
                            <option value="ref3" {{ ( isset($data['in']) && in_array('ref3', $data['in']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref3'])) && !empty($project->ref_names['ref3']) ? $project->ref_names['ref3'] : 'REF3' }}</option>
                            <option value="ref4" {{ ( isset($data['in']) && in_array('ref4', $data['in']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref4'])) && !empty($project->ref_names['ref4']) ? $project->ref_names['ref4'] : 'REF4' }}</option>
                            <option value="ref5" {{ ( isset($data['in']) && in_array('ref5', $data['in']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref5'])) && !empty($project->ref_names['ref5']) ? $project->ref_names['ref5'] : 'REF5' }}</option>
                            <option value="created_at" {{ ( isset($data['in']) && in_array('created_at', $data['in']) ) ? 'selected' : '' }}>Created</option>
                          </select>
                        </div>
                        <div class="inline-block relative w-48 px-2">
                          <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="search-fields">
                            Show fields
                          </label>
                          <select name="show[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" multiple>
                            <option value="" {{ ( !isset($data['show']) || $data['show'][0] == null ) ? 'selected' : '' }}>All fields</option>
                            <option value="id" {{ ( isset($data['show']) && in_array('id', $data['show']) ) ? 'selected' : '' }}>ID</option>
                            <option value="account" {{ ( isset($data['show']) && in_array('account', $data['show']) ) ? 'selected' : '' }}>Account</option>
                            <option value="event" {{ ( isset($data['show']) && in_array('event', $data['show']) ) ? 'selected' : '' }}>Event</option>
                            <option value="status" {{ ( isset($data['show']) && in_array('status', $data['show']) ) ? 'selected' : '' }}>Status</option>
                            <option value="description" {{ ( isset($data['show']) && in_array('description', $data['show']) ) ? 'selected' : '' }}>Description</option>
                            <option value="value" {{ ( isset($data['show']) && in_array('value', $data['show']) ) ? 'selected' : '' }}>Value</option>
                            <option value="custom_content" {{ ( isset($data['show']) && in_array('custom_content', $data['show']) ) ? 'selected' : '' }}>Custom Content</option>
                            <option value="ref1" {{ ( isset($data['show']) && in_array('ref1', $data['show']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref1'])) && !empty($project->ref_names['ref1']) ? $project->ref_names['ref1'] : 'REF1' }}</option>
                            <option value="ref2" {{ ( isset($data['show']) && in_array('ref2', $data['show']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref2'])) && !empty($project->ref_names['ref2']) ? $project->ref_names['ref2'] : 'REF2' }}</option>
                            <option value="ref3" {{ ( isset($data['show']) && in_array('ref3', $data['show']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref3'])) && !empty($project->ref_names['ref3']) ? $project->ref_names['ref3'] : 'REF3' }}</option>
                            <option value="ref4" {{ ( isset($data['show']) && in_array('ref4', $data['show']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref4'])) && !empty($project->ref_names['ref4']) ? $project->ref_names['ref4'] : 'REF4' }}</option>
                            <option value="ref5" {{ ( isset($data['show']) && in_array('ref5', $data['show']) ) ? 'selected' : '' }}>{{ (isset($project->ref_names['ref5'])) && !empty($project->ref_names['ref5']) ? $project->ref_names['ref5'] : 'REF5' }}</option>
                            <option value="created_at" {{ ( isset($data['show']) && in_array('created_at', $data['show']) ) ? 'selected' : '' }}>Created</option>
                          </select>
                        </div>
                        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 rounded-lg text-sm px-5 py-2.5">Submit</button>
                    </div>
                </form>

                <div class="overflow-x">
                <table class="table-auto w-full border-collapse border border-slate-400">
                  <thead>
                    <tr>
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('id', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">ID</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('account', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">Account</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('event', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">Event</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('status', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">Status</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('description', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">Description</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('value', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">Value</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('custom_content', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">Custom Content</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref1', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">{{ (isset($project->ref_names['ref1'])) && !empty($project->ref_names['ref1']) ? $project->ref_names['ref1'] : 'REF1' }}</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref2', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">{{ (isset($project->ref_names['ref2'])) && !empty($project->ref_names['ref2']) ? $project->ref_names['ref2'] : 'REF2' }}</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref3', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">{{ (isset($project->ref_names['ref3'])) && !empty($project->ref_names['ref3']) ? $project->ref_names['ref3'] : 'REF3' }}</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref4', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">{{ (isset($project->ref_names['ref4'])) && !empty($project->ref_names['ref4']) ? $project->ref_names['ref4'] : 'REF4' }}</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref5', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">{{ (isset($project->ref_names['ref5'])) && !empty($project->ref_names['ref5']) ? $project->ref_names['ref5'] : 'REF5' }}</th>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('created_at', $data['show']) )
                        <th class="bg-slate-200 border border-slate-300 p-4">Created</th>
                      @endif
                    </tr>
                  </thead>
                  <tbody>
                    @foreach ($records as $record)
                    <tr>
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('id', $data['show']) )
                        <td class="border border-slate-300 p-2 text-center">{{ $record->id }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('account', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->account }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('event', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->event }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('status', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->status }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('description', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->description }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('value', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->value }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('custom_content', $data['show']) )
                        <td class="border border-slate-300 p-2"><div class="break-all overflow-scroll h-full max-h-32">{{ $record->custom_content }}</div></td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref1', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->ref1 }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref2', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->ref2 }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref3', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->ref3 }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref4', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->ref4 }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('ref5', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->ref5 }}</td>
                      @endif
                      @if( !isset($data['show']) || $data['show'][0] == null || in_array('created_at', $data['show']) )
                        <td class="border border-slate-300 p-2">{{ $record->created_at }}</td>
                      @endif
                    </tr>
                    @endforeach
                  </tbody>
                </table>
                </div>

                <div class="d-flex justify-content-center p-2">
                    @php
                      $records->appends([
                        'search' => @$data['search'],
                        'in' => @$data['in'],
                        'show' => @$data['show'],
                      ]);
                    @endphp
                    @if( $records->total() < $records->perPage() )
                    <p class="text-sm text-gray-700 leading-5 dark:text-gray-400">
                        Showing {{ $records->total() }} results
                    </p>
                    @else
                        {{ $records->links() }}
                    @endif
                </div>

            </div>
        </div>
    </div>
</x-app-layout>
