<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">

          <div class="py-8">

            <div class="py-8 max-w-7xl mx-auto sm:px-6 lg:px-8">
              <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                  {{ __('Project Details') }}
              </h2>
            </div>

              <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                  <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                      <p class="text-2xl py-2"><strong>Name:</strong> {{ $project->name }}</p>
                      <p class="text-2xl py-2"><strong>Key:</strong> {{ $project->api_key }}</p>
                  </div>
              </div>
          </div>
          <div class="py-8">
              <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                  <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">

                      <h2 class="text-2xl">Records</h2>

                      <form class="bg-white shadow-md rounded my-4" method="post">
                          @csrf
                          <div class="mb-4 flex items-start">
                              <div class="inline-block relative w-full px-2">
                                  <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="search-text">
                                      Search text
                                    </label>
                                  <input name="search" id="search-text" value="{{ @$data['search'] }}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="username" type="text" placeholder="search...">
                              </div>
                              <div class="inline-block relative w-48 px-2">
                                <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="search-fields">
                                  Search in
                                </label>
                                <select name="in[]" id="search-fields" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" multiple>
                                  <option value="" {{ ( !isset($data['in']) || $data['in'][0] == null ) ? 'selected' : '' }}>All fields</option>
                                  <option value="id" {{ ( isset($data['in']) && in_array('id', $data['in']) ) ? 'selected' : '' }}>ID</option>
                                  <option value="account" {{ ( isset($data['in']) && in_array('account', $data['in']) ) ? 'selected' : '' }}>Account</option>
                                  <option value="event" {{ ( isset($data['in']) && in_array('event', $data['in']) ) ? 'selected' : '' }}>Event</option>
                                  <option value="status" {{ ( isset($data['in']) && in_array('status', $data['in']) ) ? 'selected' : '' }}>Status</option>
                                  <option value="description" {{ ( isset($data['in']) && in_array('description', $data['in']) ) ? 'selected' : '' }}>Description</option>
                                  <option value="value" {{ ( isset($data['in']) && in_array('value', $data['in']) ) ? 'selected' : '' }}>Value</option>
                                  <option value="custom_content" {{ ( isset($data['in']) && in_array('custom_content', $data['in']) ) ? 'selected' : '' }}>Custom Content</option>
                                  <option value="created_at" {{ ( isset($data['in']) && in_array('created_at', $data['in']) ) ? 'selected' : '' }}>Created</option>
                                </select>
                              </div>
                              <div class="inline-block relative w-48 px-2">
                                <label class="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2" for="search-fields">
                                  Show fields
                                </label>
                                <select name="show[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" multiple>
                                  <option value="" {{ ( !isset($data['show']) || $data['show'][0] == null ) ? 'selected' : '' }}>All fields</option>
                                  <option value="id" {{ ( isset($data['show']) && in_array('id', $data['show']) ) ? 'selected' : '' }}>ID</option>
                                  <option value="account" {{ ( isset($data['show']) && in_array('account', $data['show']) ) ? 'selected' : '' }}>Account</option>
                                  <option value="event" {{ ( isset($data['show']) && in_array('event', $data['show']) ) ? 'selected' : '' }}>Event</option>
                                  <option value="status" {{ ( isset($data['show']) && in_array('status', $data['show']) ) ? 'selected' : '' }}>Status</option>
                                  <option value="description" {{ ( isset($data['show']) && in_array('description', $data['show']) ) ? 'selected' : '' }}>Description</option>
                                  <option value="value" {{ ( isset($data['show']) && in_array('value', $data['show']) ) ? 'selected' : '' }}>Value</option>
                                  <option value="custom_content" {{ ( isset($data['show']) && in_array('custom_content', $data['show']) ) ? 'selected' : '' }}>Custom Content</option>
                                  <option value="created_at" {{ ( isset($data['show']) && in_array('created_at', $data['show']) ) ? 'selected' : '' }}>Created</option>
                                </select>
                              </div>
                              <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 rounded-lg text-sm px-5 py-2.5">Submit</button>
                          </div>
                      </form>


                      <table class="table-auto w-full border-collapse border border-slate-400">
                        <thead>
                          <tr>
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('id', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">ID</th>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('account', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">Account</th>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('event', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">Event</th>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('status', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">Status</th>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('description', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">Description</th>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('value', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">Value</th>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('custom_content', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">Custom Content</th>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('created_at', $data['show']) )
                              <th class="bg-slate-200 border border-slate-300 p-4">Created</th>
                            @endif
                          </tr>
                        </thead>
                        <tbody>
                          @foreach ($records as $record)
                          <tr>
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('id', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->id }}</td>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('account', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->account }}</td>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('event', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->event }}</td>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('status', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->status }}</td>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('description', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->description }}</td>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('value', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->value }}</td>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('custom_content', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->custom_content }}</td>
                            @endif
                            @if( !isset($data['show']) || $data['show'][0] == null || in_array('created_at', $data['show']) )
                              <td class="border border-slate-300 p-4">{{ $record->created_at }}</td>
                            @endif
                          </tr>
                          @endforeach
                        </tbody>
                      </table>

                  </div>
              </div>
          </div>

            </main>
        </div>
    </body>
</html>
