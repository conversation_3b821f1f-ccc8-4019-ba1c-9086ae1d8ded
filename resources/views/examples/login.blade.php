<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Login Webhook</title>

    <!-- Pico.css -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/@picocss/pico@2.0.6/css/pico.classless.min.css"
    />
  </head>

  <body>

    <main>

      <br/>
      <br/>
      <br/>
      <br/>

      @if( $loggedIn )

        <section id="form" style="max-width: 500px; margin: 0 auto;">
          <h2>Login Success !!!</h2>
        </section>

      <?php else: ?>

      <section id="form" style="max-width: 500px; margin: 0 auto;">

        <form method="post">
          @csrf
          <h2>Login</h2>

          @if( $error )
            <p style="color:red">Invalid Username or Password.</p>
          @endif

          <label for="text">Username</label>
          <input type="text" id="username" name="username" placeholder="username" value="user">

          <label for="text">Password</label>
          <input type="password" id="password" name="password" placeholder="password" value="secret">

          <input type="submit" value="Login">

        </form>
      </section>

      @endif

    </main>

  </body>
</html>
