<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Record extends Model
{
    use HasFactory;

    public $timestamps = true;

    protected $fillable = [
        'project_id',
        'account',
        'event',
        'status',
        'description',
        'value',
        'custom_content',
        'ref1',
        'ref2',
        'ref3',
        'ref4',
        'ref5',
    ];

    /**
     * Get the porject that owns the record.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

}
