<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Project extends Model
{
    use HasFactory;

    public $timestamps = true;

    protected $fillable = [
        'name',
        'api_key',
        'timezone',
        'ref_names',
        'per_page'
    ];

    protected $casts = [
        'ref_names' => 'array'
    ];

    /**
     * Get the records for the project.
     */
    public function records(): HasMany
    {
        return $this->hasMany(Record::class);
    }

}
