<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SummaryView extends Model
{
    use HasFactory;

    public $timestamps = true;

    protected $fillable = [
        'project_id',
        'name',
        'search',
        'in',
        'show',
        'date',
    ];

    protected $casts = [
        'date' => 'date',
        'in' => 'array',
        'show' => 'array',
    ];

    /**
     * Get the project that owns the summary view.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
}
