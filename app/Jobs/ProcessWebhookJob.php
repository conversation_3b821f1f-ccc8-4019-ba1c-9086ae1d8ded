<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Webhook;

class ProcessWebhookJob implements ShouldQueue
{
    use Queueable;

    private $data;


    /**
     * Create a new job instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Process the webhook payload asynchronously
        // Perform actions based on the webhook data

Log::info($this->data);

        Webhook::create([
            'name' => 'demo webhook',
            'request' => json_encode($this->data)
        ]);

    }

}
