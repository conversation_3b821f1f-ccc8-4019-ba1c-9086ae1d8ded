<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Record;

class ProcessRecordsWebhookJob implements ShouldQueue
{
    use Queueable;

    private $data;


    /**
     * Create a new job instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Process the webhook payload asynchronously
        // Perform actions based on the webhook data

Log::info('ProcessRecordsWebhookJob@handle():$this->data');
Log::info($this->data);

        Record::create([
            'project_id' => @$this->data['project_id'] ?? 0,
            'account' => @$this->data['account'] ?? '',
            'event' => @$this->data['event'] ?? '',
            'status' => @$this->data['status'] ?? '',
            'description' => @$this->data['description'] ?? '',
            'value' => @$this->data['value'] ?? '',
            'custom_content' => @$this->data['custom_content'] ?? '',
            'ref1' => @$this->data['ref1'] ?? '',
            'ref2' => @$this->data['ref2'] ?? '',
            'ref3' => @$this->data['ref3'] ?? '',
            'ref4' => @$this->data['ref4'] ?? '',
            'ref5' => @$this->data['ref5'] ?? '',
        ]);

    }

}
