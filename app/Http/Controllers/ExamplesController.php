<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ExamplesController extends Controller
{

    public function login()
    {

        $loggedIn = false;
        $error = false;

        $data = request()->all();
        if( !empty($data) ) {

            // Some authentication logic
            if( $data['username'] == 'user' && $data['password'] == 'secret' ) {

                $url = "http://localhost/records/endpoint";
                $payload = [
                  'key' => '1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij',
                  "account" => 'example-acc',
                  "event" => "user_login_success",
                  "status" => "success",
                  "description" => "User successfully logged in.",
                  "value" => 'example value',
                  "custom_content" => json_encode(['username' => $data['username'], 'key' => '1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij',])
                ];
                $response = $this->curlPost($url, $payload);

                $loggedIn = true;

            } else {

                $url = "http://localhost/records/endpoint";
                $payload = [
                  'key' => '1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij',
                  "account" => 'example-acc',
                  "event" => "user_login_error",
                  "status" => "error",
                  "description" => "User fails to log in.",
                  "value" => 'example value',
                  "custom_content" => json_encode(['username' => $data['username'], 'key' => '1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij1234567890abcdefghij',])
                ];
                $this->curlPost($url, $payload);

                $error = true;
            }

        }

        return view('examples.login', compact('loggedIn', 'error'));

    }

    public function logs()
    {

        $data = request()->all();

        $url = "http://localhost/api/login";
        $payload = [
            'email' => env('GENIUS_EMAIL', '<EMAIL>'),
            'password' => env('GENIUS_PASSWORD', 'Pass@1234'),
        ];

        $response = $this->curlPost($url, $payload);

        $token = ( isset($response['response']->data->access_token) ) ? $response['response']->data->access_token : false;

        if( empty($token) ) {
            die('Missing token.');
        }

        $url = "http://localhost/api/projects/1";
        $payload = $data;

        $response = $this->curlPost($url, $payload, $token);

        if( !isset($response['response']->data) ) {
            die('Missing data.');
        }

        $project = $response['response']->data->project;
        $records = $response['response']->data->records->data;

        return view('examples.logs', compact('project', 'records', 'data'));


    }


    private function curlPost($url, $payload, $token = null)
    {

                $ch = curl_init();
                $headers  = [
                    'Content-Type: application/json',
                ];

                if( $token ) {
                    $headers[] = 'Authorization: Bearer ' . $token;
                }

                //curl_setopt($ch, CURLOPT_URL, "https://genius.leapinglogic.com/records/endpoint");
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                $response = curl_exec ($ch);
                // dump($response) // {"success":true}

                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                // dump($status) // 200

                return [
                    'status' => $status,
                    'response' => json_decode($response),
                ];

    }


}
