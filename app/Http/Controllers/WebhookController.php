<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Jobs\ProcessWebhookJob;
use App\Jobs\ProcessRecordsWebhookJob;
use App\Models\Project;

class WebhookController extends Controller
{
    public function handle(Request $request)
    {
        // verify the payload authenticity

        // Send payload to job for processing
        ProcessWebhookJob::dispatch($request->all());

        return response()->json(['success' => true]);
    }

    public function records(Request $request)
    {

        $key = $request->input('key');

        if( $request->header('key') ) {
            $key = $request->header('key');
        }

        if( $request->header('apiKey') ) {
            $key = $request->header('apiKey');
        }


        // verify the payload authenticity
        if( empty($key) ) {
            return response()->json([
                'success' => false,
                'message' => 'Missing key.',
            ], 403);
        }

        $project = Project::where('api_key', $key)->first();

        if( empty($project) ) {
           return response()->json([
                'success' => false,
                'message' => 'Invalid key.',
            ], 403);
        }

        $request->merge(['project_id' => $project->id]);

        // Send payload to job for processing
        ProcessRecordsWebhookJob::dispatch($request->all());

        return response()->json(['success' => true]);
    }

}
