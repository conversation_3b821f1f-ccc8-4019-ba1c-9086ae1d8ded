<?php
namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Record;

class ProjectController extends Controller
{
  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function index()
  {
    $projects = Project::all();
    return view('projects.index', compact('projects'));
  }
  /**
   * Store a newly created resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\Response
   */
  public function store(Request $request)
  {
    $request->validate([
      'title' => 'required|max:255',
      'body' => 'required',
    ]);
    Project::create($request->all());
    return redirect()->route('projects.index')
      ->with('success', 'Project created successfully.');
  }
  /**
   * Update the specified resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function update(Request $request, $id)
  {

    $request->validate([
      'name' => 'required|max:255',
      'api_key' => 'required',
      'timezone' => 'required',
    ]);
    $project = Project::find($id);

    $requestAll = $request->all();

    $requestAll['ref_names'] = [
        'ref1' => $requestAll['ref1'],
        'ref2' => $requestAll['ref2'],
        'ref3' => $requestAll['ref3'],
        'ref4' => $requestAll['ref4'],
        'ref5' => $requestAll['ref5'],
    ];


    $project->update($requestAll);
    return redirect()->route('projects.index')
      ->with('success', 'Project updated successfully.');
  }
  /**
   * Remove the specified resource from storage.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function destroy($id)
  {
    $project = Project::find($id);
    $project->delete();
    return redirect()->route('projects.index')
      ->with('success', 'Project deleted successfully');
  }
  // routes functions
  /**
   * Show the form for creating a new project.
   *
   * @return \Illuminate\Http\Response
   */
  public function create()
  {
    return view('projects.create');
  }
  /**
   * Display the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
    public function show($id)
    {
        // Retrieve a single Project record by its ID.
        $project = Project::where('id', $id)
            ->first();

        if( empty($project->timezone) ) {
            $project->timezone = env('APP_TIMEZONE', 'UTC');
        }

        // Retrieve all Record records associated with the Project ID, ordered by creation date in descending order.
        $records = Record::where('project_id', $id)
            ->orderBy('created_at', 'desc');

        // Get all input data from the request.
        $data = request()->all();

        // Check if a search term is provided in the request.
        if (isset($data['search']) && !empty(trim($data['search']))) {
            // Trim the search term to remove leading/trailing whitespace.
            $searchText = trim($data['search']);

            // Split search text by spaces to search for all words
            $searchWords = array_filter(explode(' ', trim($searchText)));

            // Check if the search scope ('in') is not specified or is empty.
            if (empty($data['in']) || $data['in'][0] == null) {
                // If no specific columns are selected, search across all relevant columns.
                $records->where(function ($query) use ($searchWords) {
                    // Each field must contain all words
                    foreach ($searchWords as $word) {
                        $query->where(function ($subQuery) use ($word) {
                            $subQuery->orWhere('id', 'LIKE', '%' . $word . '%')
                                ->orWhere('account', 'LIKE', '%' . $word . '%')
                                ->orWhere('event', 'LIKE', '%' . $word . '%')
                                ->orWhere('status', 'LIKE', '%' . $word . '%')
                                ->orWhere('description', 'LIKE', '%' . $word . '%')
                                ->orWhere('value', 'LIKE', '%' . $word . '%')
                                ->orWhere('custom_content', 'LIKE', '%' . $word . '%')
                                ->orWhere('ref1', 'LIKE', '%' . $word . '%')
                                ->orWhere('ref2', 'LIKE', '%' . $word . '%')
                                ->orWhere('ref3', 'LIKE', '%' . $word . '%')
                                ->orWhere('ref4', 'LIKE', '%' . $word . '%')
                                ->orWhere('ref5', 'LIKE', '%' . $word . '%')
                                ->orWhere('created_at', 'LIKE', '%' . $word . '%');
                        });
                    }
                });
            } else {
                // If specific columns are selected for the search.
                if (!empty($data['in'])) {

                    // Apply 'LIKE' search only to the specified columns.
                    $records->where(function ($query) use ($data, $searchWords) {

                        // Each word must be found in at least one of the selected columns
                        foreach ($searchWords as $word) {
                            $query->where(function ($subQuery) use ($data, $word) {

                                // Check if each column is included in the search scope ('in') and apply the 'LIKE' search if it is.
                                if (in_array('id', $data['in'])) {
                                    $subQuery->orWhere('id', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('account', $data['in'])) {
                                    $subQuery->orWhere('account', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('event', $data['in'])) {
                                    $subQuery->orWhere('event', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('status', $data['in'])) {
                                    $subQuery->orWhere('status', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('description', $data['in'])) {
                                    $subQuery->orWhere('description', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('value', $data['in'])) {
                                    $subQuery->orWhere('value', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('custom_content', $data['in'])) {
                                    $subQuery->orWhere('custom_content', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('created_at', $data['in'])) {
                                    $subQuery->orWhere('created_at', 'LIKE', '%' . $word . '%');
                                }
                                //added search for ref1, ref2, ref3, ref4, ref5
                                if (in_array('ref1', $data['in'])) {
                                    $subQuery->orWhere('ref1', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('ref2', $data['in'])) {
                                    $subQuery->orWhere('ref2', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('ref3', $data['in'])) {
                                    $subQuery->orWhere('ref3', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('ref4', $data['in'])) {
                                    $subQuery->orWhere('ref4', 'LIKE', '%' . $word . '%');
                                }
                                if (in_array('ref5', $data['in'])) {
                                    $subQuery->orWhere('ref5', 'LIKE', '%' . $word . '%');
                                }
                            });
                        }
                    });
                }
            }
        }

        $perPage = request()->input('perPage') ?? $project->per_page;

        // Retrieve the filtered and ordered records.
        $records = request()->isMethod('post') ? $records->paginate($perPage, ['*'], 'page', 1) : $records->paginate($perPage);

        // Format the project's created_at and updated_at timestamps according to the project's timezone.
        if (!empty($project->created_at)) {
            $project->created_at = $project->created_at->setTimezone($project->timezone)->format('Y-m-d H:i:s');
        }
        if (!empty($project->updated_at)) {
            $project->updated_at = $project->updated_at->setTimezone($project->timezone)->format('Y-m-d H:i:s');
        }

        // Format the created_at timestamp of each record according to the project's timezone.
        foreach ($records as $record) {
            if (!empty($record->created_at)) {
                $record->created_at = $record->created_at->setTimezone($project->timezone)->format('Y-m-d H:i:s');
            }
        }

        // Return the 'projects.show' view, passing the project, records, and request data to the view.
        return view('projects.show', compact('project', 'records', 'data'));
    }
  /**
   * Show the form for editing the specified project.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function edit($id)
  {
    $project = Project::find($id);
    return view('projects.edit', compact('project'));
  }
}
