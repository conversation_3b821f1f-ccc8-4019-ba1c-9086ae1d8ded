<?php
namespace App\Http\Controllers\API;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Record;

class ProjectController extends Controller
{
  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function index()
  {

    $perPage = request()->input('perPage') ?? 15;

    $projects = Project::paginate($perPage);

    return $this->sendResponse($projects, 'Projects retrieved successfully.');
  }

  /**
   * Display the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function show($id)
  {

    $project = Project::where('id', $id)
                ->first();

    if( empty($project->timezone) ) {
        $project->timezone = env('APP_TIMEZONE', 'UTC');
    }

    $records = Record::where('project_id', $id)
                ->orderBy('created_at', 'desc');

    $data = request()->all();

    if( isset($data['search']) && !empty( trim($data['search']) ) ) {

        $searchText = trim($data['search']);
        if( empty($data['in']) || $data['in'][0] == null ) {

            $records->where(function($query) use($data, $searchText) {

                $query->orWhere('id', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('account', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('event', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('status', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('description', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('value', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('custom_content', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('ref1', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('ref2', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('ref3', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('ref4', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('ref5', 'LIKE', '%'.$searchText.'%')
                      ->orWhere('created_at', 'LIKE', '%'.$searchText.'%');
            });

        } else {

            if( !empty($data['in']) ) {

                $records->where(function($query) use($data, $searchText) {

                    if( in_array('id', $data['in']) ) {
                        $query->orWhere('id', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('account', $data['in']) ) {
                        $query->orWhere('account', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('event', $data['in']) ) {
                        $query->orWhere('event', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('status', $data['in']) ) {
                        $query->orWhere('status', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('description', $data['in']) ) {
                        $query->orWhere('description', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('value', $data['in']) ) {
                        $query->orWhere('value', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('custom_content', $data['in']) ) {
                        $query->orWhere('custom_content', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('ref1', $data['in']) ) {
                        $query->orWhere('ref1', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('ref2', $data['in']) ) {
                        $query->orWhere('ref2', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('ref3', $data['in']) ) {
                        $query->orWhere('ref3', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('ref4', $data['in']) ) {
                        $query->orWhere('ref4', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('ref5', $data['in']) ) {
                        $query->orWhere('ref5', 'LIKE', '%'.$searchText.'%');
                    }
                    if( in_array('created_at', $data['in']) ) {
                        $query->orWhere('created_at', 'LIKE', '%'.$searchText.'%');
                    }

                });

            }

        }

    }

    if( !empty($data['show']) && $data['show'][0] != null ) {

        $records->select( $data['show'] );

    }

    $perPage = request()->input('perPage') ?? 15;

    $records = $records->paginate($perPage);


    if( !empty($project->created_at) ) {
        $project->created_at = $project->created_at->setTimezone($project->timezone)->format('Y-m-d H:i:s');
    }
    if( !empty($project->updated_at) ) {
        $project->updated_at = $project->updated_at->setTimezone($project->timezone)->format('Y-m-d H:i:s');
    }

    foreach($records->items() as $record) {
        if( !empty($record->created_at) ) {
            $record->created_at = $record->created_at->setTimezone($project->timezone)->format('Y-m-d H:i:s');
        }
        // if( isset($project->ref_names['ref1']) && !empty($project->ref_names['ref1']) ) {
        //     $record->{$project->ref_names['ref1']} = $record->ref1;
        //     unset($record->ref1);
        // }
        // if( isset($project->ref_names['ref2']) && !empty($project->ref_names['ref2']) ) {
        //     $record->{$project->ref_names['ref2']} = $record->ref2;
        //     unset($record->ref2);
        // }
        // if( isset($project->ref_names['ref3']) && !empty($project->ref_names['ref3']) ) {
        //     $record->{$project->ref_names['ref3']} = $record->ref3;
        //     unset($record->ref3);
        // }
        // if( isset($project->ref_names['ref4']) && !empty($project->ref_names['ref4']) ) {
        //     $record->{$project->ref_names['ref4']} = $record->ref4;
        //     unset($record->ref4);
        // }
        // if( isset($project->ref_names['ref5']) && !empty($project->ref_names['ref5']) ) {
        //     $record->{$project->ref_names['ref5']} = $record->ref5;
        //     unset($record->ref5);
        // }
    }

    return $this->sendResponse(
        [
        'project' => $project,
        'records' => $records,
        'params' => $data,
        ]
        , 'Projects details retrieved successfully.'
    );

  }

}
