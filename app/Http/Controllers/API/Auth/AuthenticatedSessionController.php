<?php
namespace App\Http\Controllers\Api\Auth;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\API\Controller;

class AuthenticatedSessionController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ]);

        if (!Auth::attempt($request->only('email', 'password'))) {
            return response()->json(['message' => 'Invalid login credentials'], 401);
        }

        $user = Auth::user();
        $token = $user->createToken('auth_token')->plainTextToken;

        return $this->sendResponse(
            [
                'access_token' => $token,
                'token_type' => 'Bearer',
                'user' => $user,
            ],
            'Login successful.'
        );

    }

    public function destroy(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return $this->sendResponse('', 'Logout successful.');
    }
}
