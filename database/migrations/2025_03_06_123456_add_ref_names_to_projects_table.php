<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       if (!Schema::hasColumn('projects', 'ref_names')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->text('ref_names')->nullable()->after('timezone');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('projects', 'ref_names')) {
            Schema::table('projects', function($table) {
                $table->dropColumn('ref_names');
            });
        }
    }
};
