<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       if (!Schema::hasColumn('records', 'ref1')) {
            Schema::table('records', function (Blueprint $table) {
                $table->string('ref1', length: 255)->nullable()->after('custom_content');
            });
        }

        if (!Schema::hasColumn('records', 'ref2')) {
            Schema::table('records', function (Blueprint $table) {
                $table->string('ref2', length: 255)->nullable()->after('ref1');
            });
        }

        if (!Schema::hasColumn('records', 'ref3')) {
            Schema::table('records', function (Blueprint $table) {
                $table->string('ref3', length: 255)->nullable()->after('ref2');
            });
        }

        if (!Schema::hasColumn('records', 'ref4')) {
            Schema::table('records', function (Blueprint $table) {
                $table->string('ref4', length: 255)->nullable()->after('ref3');
            });
        }

        if (!Schema::hasColumn('records', 'ref5')) {
            Schema::table('records', function (Blueprint $table) {
                $table->string('ref5', length: 255)->nullable()->after('ref4');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('records', function($table) {
            $table->dropColumn('ref1');
            $table->dropColumn('ref2');
            $table->dropColumn('ref3');
            $table->dropColumn('ref4');
            $table->dropColumn('ref5');
        });
    }
};
