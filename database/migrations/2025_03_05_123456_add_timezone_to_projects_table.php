<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       if (!Schema::hasColumn('projects', 'timezone')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->string('timezone', length: 100)->nullable()->after('api_key');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('projects', 'timezone')) {
            Schema::table('projects', function($table) {
                $table->dropColumn('timezone');
            });
        }
    }
};
