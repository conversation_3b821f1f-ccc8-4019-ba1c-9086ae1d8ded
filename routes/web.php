<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\ExamplesController;

Route::get('/', function () {
    return view('welcome');
});

// Route::get('/dashboard', function () {
//     return view('dashboard');
// })->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::post('webhook/endpoint', [WebhookController::class, 'handle']);
Route::post('records/endpoint', [WebhookController::class, 'records']);

Route::get('/dashboard', ProjectController::class .'@index')->middleware(['auth', 'verified'])->name('dashboard');;

// returns the home page with all projects
Route::get('/projects', ProjectController::class .'@index')->middleware(['auth', 'verified'])->name('projects.index');
// returns the form for adding a project
Route::get('/projects/create', ProjectController::class . '@create')->name('projects.create');
// adds a project to the database
Route::post('/projects', ProjectController::class .'@store')->name('projects.store');
// returns a page that shows a full project
Route::get('/projects/{project}', ProjectController::class .'@show')->middleware(['auth', 'verified'])->name('projects.show');
Route::post('/projects/{project}', ProjectController::class .'@show')->middleware(['auth', 'verified'])->name('projects.show');
// returns the form for editing a project
Route::get('/projects/{project}/edit', ProjectController::class .'@edit')->name('projects.edit');
// updates a project
Route::put('/projects/{project}', ProjectController::class .'@update')->name('projects.update');
// deletes a project
Route::delete('/projects/{project}', ProjectController::class .'@destroy')->name('projects.destroy');

Route::get('/examples/login', ExamplesController::class .'@login')->name('examples.login');
Route::post('/examples/login', ExamplesController::class .'@login')->name('examples.login');
Route::get('/examples/logs', ExamplesController::class .'@logs')->name('examples.logs');
Route::post('/examples/logs', ExamplesController::class .'@logs')->name('examples.logs');



require __DIR__.'/auth.php';
