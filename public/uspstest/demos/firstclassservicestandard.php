<?php

use USPS\FirstClassServiceStandards;

require_once('autoload.php');

// Initiate and set the username provided from usps.
$delivery = new FirstClassServiceStandards('xxxx');

// During test mode this seems not to always work as expected.
$delivery->setTestMode(true);

// Add the zip codes we want to know a shipping time between.
$delivery->addRoute('91730', '90025');

// Perform the call and print out the results.
try {
    var_dump($delivery->getServiceStandard());
} catch (Exception $e) {
    // Handle any errors.
}
var_dump($delivery->getArrayResponse());

// Check if it was completed
if ($delivery->isSuccess()) {
    echo 'Done';
} else {
    echo 'Error: '.$delivery->getErrorMessage();
}
